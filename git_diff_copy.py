#!/usr/bin/env python3
"""
gitリポジトリの差分ファイルをコピーする
./change_dataに保存

使用方法:
1. 第1引数: 比較元（古い）のコミットSHAを入力
2. 第2引数: 比較先（新しい）のコミットSHAを入力（省略時は最新コミット）

例:
- 特定の2つのコミット間の差分: SHA1を入力 → SHA2を入力
- 特定のコミットから最新までの差分: SHA1を入力 → Enterキー（空白）
"""

import os
import subprocess
import shutil

# 対象のディレクトリを指定
TARGET_DIR = "/home/<USER>/projects/wordpress-bizcan"
# 出力先ディレクトリを固定
DEST_DIR = "./change_data"


def run_git_command(args, cwd):
    """
    Gitコマンドを実行し、結果を返す

    Args:
        args (list): Gitコマンドの引数リスト
        cwd (str): コマンドを実行するディレクトリ

    Returns:
        str: コマンドの標準出力
    """
    print(f"Running command: {' '.join(args)} in {cwd}")  # デバッグメッセージ
    result = subprocess.run(
        args, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, cwd=cwd
    )
    if result.returncode != 0:
        print(f"Error: {result.stderr}")
        raise subprocess.CalledProcessError(result.returncode, args)
    return result.stdout.strip()


def get_latest_commit_sha():
    """
    現在のブランチの最新コミットのSHAを取得します。

    Returns:
        str: 最新コミットのSHA
    """
    return run_git_command(["git", "rev-parse", "HEAD"], TARGET_DIR)


def get_diff_files(new_sha, old_sha):
    """
    2つのgitコミット間で異なるファイルのリストを取得します。

    Args:
        new_sha (str): 新しいコミットのSHA
        old_sha (str): 古いコミットのSHA

    Returns:
        list: 異なるファイルのリスト
    """
    output = run_git_command(
        ["git", "diff", "--name-only", old_sha, new_sha],
        TARGET_DIR,
    )
    print(f"Diff files: {output}")  # デバッグメッセージ
    return output.splitlines()


def get_deleted_and_renamed_files(new_sha, old_sha):
    """
     2つのgitコミット間で削除されたファイルと名前変更されたファイルのリストを取得します。

    Args:
        new_sha (str): 新しいコミットのSHA
        old_sha (str): 古いコミットのSHA

    Returns:
        list: 削除されたファイルと名前変更されたファイルのリスト
    """
    output = run_git_command(
        ["git", "diff", "--name-status", "--diff-filter=DR", f"{old_sha}..{new_sha}"],
        TARGET_DIR,
    )
    return output.splitlines()


def copy_files(files, destination, exclude_files=None):
    """
    指定されたファイルのリストを出力先ディレクトリにコピーし、
    ディレクトリ構造を保持します。

    Args:
        files (list): コピーするファイルのリスト
        destination (str): 出力先ディレクトリのパス
        exclude_files (set): 除外するファイルのセット
    """
    exclude_files = exclude_files or set()  # 除外リストを初期化
    for file in files:
        if file in exclude_files:  # 除外リストに含まれている場合はスキップ
            continue
        file_path = os.path.join(TARGET_DIR, file)
        if os.path.exists(file_path):
            dest_path = os.path.join(destination, file)
            os.makedirs(os.path.dirname(dest_path), exist_ok=True)
            shutil.copy2(file_path, dest_path)
        else:
            print(f"Warning: {file} does not exist and will be skipped.")


def validate_commit_sha(sha, cwd):
    """
    指定されたSHAが有効なコミットかどうかを検証します。
    
    Args:
        sha (str): 検証するSHA
        cwd (str): コマンドを実行するディレクトリ
    
    Returns:
        bool: 有効な場合True、無効な場合False
    """
    try:
        run_git_command(["git", "rev-parse", "--verify", sha], cwd)
        return True
    except subprocess.CalledProcessError:
        return False


def document():
    # 第1引数：比較元のSHA（古い）を入力
    while True:
        old_sha = input("Enter the SHA of the older commit (source): ").strip()
        if not old_sha:
            print("Error: SHA cannot be empty. Please enter a valid commit SHA.")
            continue
        if validate_commit_sha(old_sha, TARGET_DIR):
            break
        else:
            print(f"Error: '{old_sha}' is not a valid commit SHA. Please try again.")
    
    # 第2引数：比較先のSHA（新しい）を入力、未入力時はHEAD
    while True:
        new_sha_input = input("Enter the SHA of the newer commit (target) [default: HEAD]: ").strip()
        
        if new_sha_input:
            if validate_commit_sha(new_sha_input, TARGET_DIR):
                new_sha = new_sha_input
                print(f"Using specified commit: {new_sha}")
                break
            else:
                print(f"Error: '{new_sha_input}' is not a valid commit SHA. Please try again.")
        else:
            new_sha = get_latest_commit_sha()
            print(f"Using latest commit (HEAD): {new_sha}")
            break

    # 出力先ディレクトリを削除してから再度作成
    if os.path.exists(DEST_DIR):
        shutil.rmtree(DEST_DIR)
    os.makedirs(DEST_DIR)

    # 差分ファイルを取得（old_sha → new_sha の変更）
    print(f"\nComparing changes from {old_sha[:8]} to {new_sha[:8]}...")
    diff_files = get_diff_files(new_sha, old_sha)
    
    if not diff_files:
        print("No differences found between the two commits.")
        return
    
    print(f"Found {len(diff_files)} changed files")

    # 除外するファイルを指定
    exclude_files = {".env"}
    copy_files(diff_files, DEST_DIR, exclude_files)
    print(f"\nSuccessfully copied {len(diff_files)} files to {DEST_DIR}")

    # 削除されたファイルのリストを取得してテキストファイルに出力
    deleted_files = get_deleted_and_renamed_files(new_sha, old_sha)
    with open(os.path.join(DEST_DIR, "deleted_files.txt"), "w") as f:
        for line in deleted_files:
            # タブで分割し、最初の要素（ステータス）と最後の要素（ファイル名）を取得
            parts = line.split("\t")
            if parts:  # 空行でないことを確認
                status = parts[0]
                file = parts[-1]  # 最後の要素をファイル名として使用
                if status == "D":
                    f.write(f"{file}\n")
    print(f"Deleted files list saved to {os.path.join(DEST_DIR, 'deleted_files.txt')}")
    print(f"\nOperation completed successfully!")


if __name__ == "__main__":
    document()
